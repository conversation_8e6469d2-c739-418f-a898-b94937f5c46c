// import { PrismaClient } from "@prisma/client";

// const prisma = new PrismaClient();

// const globalForPrisma = global as unknown as { prisma: typeof prisma };

// if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// export default prisma;

import { PrismaClient } from "@/generated"

const globalForPrisma = globalThis as unknown as { prisma: PrismaClient }

const prisma = globalForPrisma.prisma || new PrismaClient()
export default prisma

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma
