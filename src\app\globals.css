@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";

@theme {
  --background-image-primary-gradient-x: linear-gradient(
    to right,
    #4e0971,
    #aa2556
  );
  --background-image-primary-gradient-y: linear-gradient(
    to top,
    #4e0971,
    #aa2556
  );

  --color-background: #fafafa;
  --color-foreground: #3e384d;

  --color-primary: #aa2556;
  --color-primary-foreground: #fafafa;

  --color-secondary: #3e384d;
  --color-secondary-foreground: #fafafa;

  --color-muted: #676079;
  --color-muted-foreground: #fafafa;

  --color-destructive: #e40808;
  --color-destructive-foreground: #fafafa;

  --color-paid: #feb602;
  --color-paid-foreground: #121212;

  --color-border: #676079;
  --color-input: #3e384d;
  --color-ring: #aa2556;

  --radius-lg: 0.5rem;
  --radius-md: calc(0.5rem - 2px);
  --radius-sm: calc(0.5rem - 4px);
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: #aa2556;
  border-radius: 5px;
}
* {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #aa2556 transparent;
  text-rendering: geometricPrecision;
}

/* font-["Noto_Kufi_Arabic",Tajawal,sans-serif] */
@layer base {
  html {
    @apply bg-secondary font-[Noto_Kufi_Arabic];
  }
  body {
    @apply bg-background text-secondary;
  }
  * {
    @apply border-border;
  }
  svg,
  img {
    @apply select-none;
  }
  img {
    @apply object-cover;
  }
  h1,
  h2 {
    @apply leading-relaxed;
  }
}
/* 
@layer components {
  .textHTML h1 {
    @apply scroll-m-20 text-4xl font-extrabold tracking-tight;
  }
  .textHTML h2 {
    @apply scroll-m-20 border-b pb-1 text-3xl font-semibold tracking-tight first:mt-0;
  }
  .textHTML h3 {
    @apply scroll-m-20 text-2xl font-semibold tracking-tight;
  }
  .textHTML h4 {
    @apply scroll-m-20 text-xl font-semibold tracking-tight;
  }
  .textHTML p {
    @apply leading-7;
  }
  .textHTML blockquote {
    @apply mt-6 border-l-2 pl-6 italic;
  }
  .textHTML ul {
    @apply my-6 mr-6 list-outside list-disc [&>li]:mt-0;
  }
  .textHTML ol {
    @apply my-6 mr-6 list-outside list-decimal [&>li]:mt-0;
  }
} */

@layer components {
  .textHTML {
    @apply prose prose-gray prose-p:text-sm md:prose-p:text-base prose-p:leading-7 **:empty:hidden!;

    li p {
      @apply m-0! leading-normal;
    }
  }
}

/* Accordion animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}
