import React from "react"
import { revalidatePath, revalidateTag } from "next/cache"
import { dataLimits } from "@/utils/siteConfig"

import prisma from "@/lib/prisma"
import BooksForm from "@/components/dashboard-workspace/books-CRUD/BooksForm"
import { booksSchema } from "@/components/dashboard-workspace/books-CRUD/booksSchema"
import PageLayout from "@/components/dashboard-workspace/PageLayout"
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles"

export default function CreateBookPage() {
  return (
    <PageLayout title="إضافة كتاب">
      <BooksForm
        mode="create"
        defaultValues={{ price: 0 }}
        onAction={async ({ data: request }) => {
          "use server"

          const limit = dataLimits.books
          const { success, data, error } = booksSchema
            .omit({ id: true })
            .safeParse(request)
          if (!success)
            return { success: false, errors: error.formErrors.fieldErrors }

          const [bookUrl, coverImage] = await Promise.all([
            handleFileUpdate("", data.bookUrl, "books/pdf/"),
            handleFileUpdate("", data.coverImage, "books/image/"),
          ])

          const [totalCount] = await prisma.$transaction([
            prisma.book.count(),
            prisma.book.create({ data: { ...data, bookUrl, coverImage } }),
          ])

          const totalPages = Math.ceil(totalCount / limit)

          revalidateTag("pageTitlesAndIds")
          revalidatePath("/admin/books")
          revalidateTag("booksPagesCount")
          for (let page = 1; page <= totalPages; page++)
            revalidatePath(`/media-experience/books/${page}`)

          return { success: true, message: "تم اضافة الكتاب بنجاح" }
        }}
      />
    </PageLayout>
  )
}
