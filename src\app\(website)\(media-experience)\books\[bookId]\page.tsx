import * as React from "react"
import { Metada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { siteName } from "@/utils/siteConfig"
import { Book } from "@prisma/client"
import { CalendarDays, Download } from "lucide-react"

import prisma from "@/lib/prisma"
import BadgePrice from "@/components/ui/badge-price"
import { Button } from "@/components/ui/button"
import Comments from "@/components/ui/comments/comments"
import FormatTime from "@/components/ui/format-time"
import PageDivider from "@/components/ui/page-divider"

type bookPageProps = { params: Promise<{ bookId?: string }> }

const getBookAndRelated = React.cache(async (bookId?: string) => {
  const books = await prisma.book.findMany({
    cursor: { id: bookId },
    take: 11,
  })

  const book = books.find((book) => book.id === bookId)
  const related = books?.filter((book) => book.id !== bookId)

  if (!book) return notFound()
  return { book, related }
})

export async function generateMetadata(
  props: bookPageProps
): Promise<Metadata> {
  const bookId = (await props.params).bookId
  const { book } = await getBookAndRelated(bookId)

  return {
    title: book.title,
    description: book.seoDescription,
    keywords: book.seokeywords,
    openGraph: {
      type: "website",
      locale: "ar",
      siteName: siteName,
      images: book.coverImage,
      description: book.seoDescription,
      title: `${book.title} | د. ناهد باشطح`,
      url: `/books/${book.id}`,
    },
    twitter: {
      card: "summary_large_image",
      title: book.title,
      description: book.seoDescription,
      images: book.coverImage,
      site: siteName,
      creator: "ناهد باشطح",
    },
  }
}

// ==================================================================================
// صفحة الكتاب
// ==================================================================================
export default async function BookPage(props: bookPageProps) {
  const bookId = (await props.params).bookId
  const { book, related } = await getBookAndRelated(bookId)

  return (
    <PageDivider
      pageContents={<BookContent book={book} />}
      titleMenu="كتب ذات صلة"
      menuItems={related?.map((book) => (
        <BookMenuCard key={book.id} book={book} />
      ))}
      comments={
        <Comments
          entity="book"
          entityId={book.id}
          pathRevalidate={`/books/${book.id}`}
        />
      }
    />
  )
}

// ==================================================================================
// مكون محتوى الكتاب
// ==================================================================================
function BookContent({ book }: { book: Book }) {
  return (
    <div className="flex-1 space-y-5">
      <h1 className="text-primary text-2xl font-bold md:text-3xl">
        {book.title}
      </h1>
      <div className="bg-muted/30 h-px min-w-max"></div>
      <div className="flex gap-3">
        {/* غلاف الكتاب */}
        <div className="border-muted/0 bg-muted/30 relative aspect-[1/1.5] w-28 shrink-0 overflow-clip rounded-md border sm:w-40">
          <Image
            sizes="30vw"
            src={book.coverImage}
            className="h-auto w-full object-cover"
            alt={book.title}
            fill
          />
        </div>
        {/* التفاصيل */}
        <div className="flex flex-col space-y-2 text-sm sm:text-base">
          <span>الصفحات : {book.pagesCount}</span>
          <span>
            الســـعر : <BadgePrice price={book.price} />
          </span>
          <span>
            تاريــــخ : <FormatTime dateInput={book.createdAt} />
          </span>
          <div className="">
            <Button variant="outline" className="mt-5">
              تنزيل <Download />
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-5">
        <div className="flex w-full flex-nowrap items-center gap-4">
          <h2 id="lectures" className="text-primary scroll-mt-20">
            نبذة عن الكتاب
          </h2>
          <hr className="border-primary mt-1.5 flex-1" />
        </div>
        <p>{book.summary}</p>
      </div>
    </div>
  )
}

// ==================================================================================
// مكون بطاقة الكتاب في القائمة الكتب ذات الصلة
// ==================================================================================
function BookMenuCard({ book }: { book: Book }) {
  return (
    <Link
      href={`/books/${book.id}#pagination`}
      className="group hover:from-muted/50 flex items-start gap-3 rounded-lg bg-linear-to-r to-65% py-3"
    >
      <div className="bg-muted relative aspect-[1/1.5] w-[25%] shrink-0 overflow-clip rounded-md">
        <Image
          className="h-auto w-full scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
          sizes="(max-width: 768px) 20vw, 7vw"
          src={book.coverImage}
          alt={book.title}
          fill
        />
      </div>
      <div className="w-[64%] space-y-1 rounded-lg">
        <p className="w-full truncate text-sm font-medium">{book.title}</p>
        <p className="text-background/70 line-clamp-2 w-full text-xs">
          {book.summary}
        </p>
        <p className="text-background/70 flex gap-1 pt-1 text-[10px]">
          <CalendarDays className="size-3" />{" "}
          <FormatTime dateInput={book.createdAt} />
        </p>
      </div>
    </Link>
  )
}
