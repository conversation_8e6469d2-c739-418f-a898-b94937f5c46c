import { getComments } from "@/utils/get-data-from-db";
import { EntityType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = request.nextUrl;

  const page = Number(searchParams.get("page"));
  const entity = searchParams.get("entity") as EntityType | null;
  const entityId = searchParams.get("entity-id") as string | null;

  if (!entity || !entityId) {
    return NextResponse.json({ error: "Missing entity or entityId" }, { status: 400 });
  }

  const res = await getComments({ page, entity, entityId });

  return NextResponse.json(res);
}
