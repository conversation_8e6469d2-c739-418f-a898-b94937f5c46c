import { Metadata } from "next"
import { siteName } from "@/utils/siteConfig"

import Sidebar from "@/components/profile-components/sidebar/sidebar"

export const dynamic = "force-dynamic"

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: `الملف الشخصي | ${siteName}`,
    robots: {
      index: false,
      follow: false,
      googleBot: { index: false, follow: false },
    },
  }
}

export default function ProfileLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-svh flex-col pb-8 md:flex-row">
      <Sidebar />
      <div className="mt-10 px-3 md:mt-24 md:flex-1 md:px-6 lg:px-10">
        {children}
      </div>
    </div>
  )
}
