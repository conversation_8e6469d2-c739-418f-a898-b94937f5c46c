import * as React from "react"
import { <PERSON>ada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { siteName } from "@/utils/siteConfig"
import { BlogPost } from "@prisma/client"
import { CalendarDays } from "lucide-react"

import prisma from "@/lib/prisma"
import Comments from "@/components/ui/comments/comments"
import FormatTime from "@/components/ui/format-time"
import PageDivider from "@/components/ui/page-divider"

type PostPageProps = { params: Promise<{ postId: string }> }

export const dynamic = "force-static"

// يرجع المنشور بالإضافة الى عشرة منشورات بعده
const getPostAndRelated = React.cache(async (postId: string) => {
  const posts = await prisma.blogPost.findMany({
    cursor: { id: postId },
    take: 11,
  })

  return posts
})

export async function generateMetadata(
  props: PostPageProps
): Promise<Metadata> {
  const postId = (await props.params).postId
  const posts = await getPostAndRelated(postId)
  const post = posts?.find((post) => post.id === postId)

  if (!post) return notFound()

  return {
    title: post.title,
    description: post.seoDescription,
    keywords: post.seokeywords,
    openGraph: {
      type: "website",
      locale: "ar",
      siteName: siteName,
      images: post.image,
      description: post.seoDescription,
      url: `/blog/${post.id}`,
      title: `${post.title} | د. ناهد باشطح`,
    },
  }
}

// ==================================================================================
// صفحة عرض منشور واحد من منشورات المدونة
// ==================================================================================
export default async function PostPage(props: PostPageProps) {
  const postId = (await props.params).postId
  const posts = await getPostAndRelated(postId)

  const post = posts?.find((post) => post.id === postId)
  if (!post) return notFound()

  const related = posts?.filter((post) => post.id !== postId)

  return (
    <div className="mb-28 px-3 pt-16 lg:px-6">
      <PageDivider
        pageContents={<PostPageContent {...post} />}
        titleMenu="منشورات ذات صلة"
        menuItems={related?.map((post) => (
          <PostMenuCard key={post.id} post={post} />
        ))}
        comments={
          <Comments
            entity="blogPost"
            entityId={post.id}
            pathRevalidate={`/blog/${post.id}`}
          />
        }
      />
    </div>
  )
}

// ==================================================================================
// مكون يعرض محتوى المنشور
// ==================================================================================
function PostPageContent(post: BlogPost) {
  return (
    <div className="space-y-7 py-8">
      <h1 className="text-3xl leading-relaxed font-bold drop-shadow-[0_2px_1px_#00000050] sm:text-4xl sm:leading-relaxed">
        {post.title}
      </h1>
      <div className="relative aspect-640/426 w-3/4 overflow-clip rounded-lg">
        <Image
          src={post.image}
          className="scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
          sizes="(max-width: 768px) 75vw, 50vw"
          alt={post.title}
          fill
        />
      </div>
      <div
        className="textHTML"
        dangerouslySetInnerHTML={{ __html: post.content }}
      ></div>
    </div>
  )
}

// ==================================================================================
// مكون بطاقة المنشور في القائمة المنشورات ذات الصلة
// ==================================================================================
function PostMenuCard({ post }: { post: BlogPost }) {
  return (
    <Link
      href={`/blog/${post.id}`}
      className="flex snap-start items-start gap-3 rounded-lg bg-linear-to-r py-3"
    >
      <div className="bg-muted/80 relative aspect-640/426 w-[30%] shrink-0 overflow-clip rounded-md">
        <Image
          className="h-auto w-full scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
          sizes="(max-width: 768px) 20vw, 7vw"
          src={post.image}
          alt={post.title}
          fill
        />
      </div>
      <div className="w-[64%] space-y-1 rounded-lg">
        <p className="w-full truncate text-sm font-medium">{post.title}</p>
        <p className="text-background/70 line-clamp-2 w-full text-xs">
          {post.seoDescription}
        </p>
        <p className="text-background/70 flex gap-1 pt-1 text-[10px]">
          <CalendarDays className="size-3" />{" "}
          <FormatTime dateInput={post.createdAt} />
        </p>
      </div>
    </Link>
  )
}
