import { Metadata } from "next"
import { <PERSON><PERSON><PERSON><PERSON>, MessageCircle, <PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react"

import { getFaqs } from "@/utils/get-data-from-db"
import { siteName } from "@/utils/siteConfig"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

export const metadata: Metadata = {
  title: `الأسئلة الشائعة | ${siteName}`,
  description: "إجابات شاملة على الأسئلة الأكثر شيوعاً حول خدمات الدكتورة ناهد باشطح والعلاج الشعوري",
  keywords: [
    "أسئلة شائعة",
    "ناهد باشطح",
    "العلاج الشعوري",
    "استشارات نفسية",
    "تقنيات العلاج",
    "الصحة النفسية"
  ],
}

export default async function FaqPage() {
  const faqs = await getFaqs()

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/95">
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-primary/10 via-primary/5 to-transparent">
        <div className="absolute inset-0 bg-[url('/pattern.svg')] opacity-5"></div>
        <div className="relative mx-auto max-w-4xl px-4 py-16 text-center sm:px-6 lg:px-8">
          <div className="mb-6 inline-flex items-center justify-center rounded-full bg-primary/10 p-3">
            <HelpCircle className="h-8 w-8 text-primary" />
          </div>

          <h1 className="mb-4 text-4xl font-bold tracking-tight text-secondary sm:text-5xl lg:text-6xl">
            الأسئلة الشائعة
          </h1>

          <p className="mx-auto max-w-2xl text-lg text-muted sm:text-xl">
            إجابات شاملة على الأسئلة الأكثر شيوعاً حول خدمات الدكتورة ناهد باشطح
            والعلاج الشعوري لمساعدتك في العثور على ما تبحث عنه
          </p>
        </div>
      </div>

      {/* FAQ Content */}
      <div className="mx-auto max-w-4xl px-4 py-12 sm:px-6 lg:px-8">
        {faqs.length > 0 ? (
          <div className="space-y-6">
            <div className="mb-8 text-center">
              <div className="inline-flex items-center gap-2 rounded-full bg-primary/5 px-4 py-2 text-sm font-medium text-primary animate-pulse">
                <Sparkles className="h-4 w-4" />
                {faqs.length} سؤال وجواب
              </div>
            </div>

            <Accordion type="single" collapsible className="space-y-4">
              {faqs.map((faq, index) => (
                <AccordionItem
                  key={faq.id}
                  value={faq.id}
                  className="rounded-lg border border-border/50 bg-background/80 backdrop-blur-sm shadow-sm transition-all hover:shadow-md hover:border-primary/20"
                >
                  <AccordionTrigger className="px-6 py-4 text-right hover:no-underline">
                    <div className="flex items-start gap-3 text-right">
                      <span className="flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-primary/10 text-xs font-bold text-primary">
                        {index + 1}
                      </span>
                      <span className="text-base font-semibold text-secondary leading-relaxed">
                        {faq.question}
                      </span>
                    </div>
                  </AccordionTrigger>

                  <AccordionContent className="px-6 pb-6">
                    <div className="mr-9 text-muted leading-relaxed">
                      <div className="prose prose-sm max-w-none text-right">
                        {faq.answer.split('\n').map((paragraph, idx) => (
                          <p key={idx} className="mb-3 last:mb-0">
                            {paragraph}
                          </p>
                        ))}
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="mb-6 inline-flex items-center justify-center rounded-full bg-muted/20 p-4">
              <HelpCircle className="h-12 w-12 text-muted" />
            </div>
            <h3 className="mb-2 text-xl font-semibold text-secondary">
              لا توجد أسئلة شائعة حالياً
            </h3>
            <p className="text-muted">
              سيتم إضافة الأسئلة الشائعة قريباً لمساعدتك في العثور على الإجابات التي تحتاجها
            </p>
          </div>
        )}
      </div>

      {/* Contact Section */}
      <div className="bg-gradient-to-r from-primary/5 to-transparent">
        <div className="mx-auto max-w-4xl px-4 py-12 text-center sm:px-6 lg:px-8">
          <h2 className="mb-4 text-2xl font-bold text-secondary">
            لم تجد إجابة لسؤالك؟
          </h2>
          <p className="mb-6 text-muted">
            لا تتردد في التواصل معنا للحصول على المساعدة التي تحتاجها
          </p>
          <a
            href="/consultation"
            className="group inline-flex items-center gap-2 rounded-lg bg-primary px-6 py-3 text-sm font-medium text-primary-foreground shadow-md transition-all hover:bg-primary/90 hover:shadow-lg hover:scale-105"
          >
            <MessageCircle className="h-4 w-4" />
            احجز استشارة
            <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
          </a>
        </div>
      </div>
    </div>
  )
}