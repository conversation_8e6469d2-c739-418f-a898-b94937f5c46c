"use client"

import React from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

import { fromEntries } from "../../lib/utils"
import { Button } from "../ui/Button"
import DataFormField from "./DataFormField"
import { DataFormPropsDef } from "./types"

export default function DataForm<TData extends { [K: string]: any }>(
  props: DataFormPropsDef<TData>
) {
  const router = useRouter()
  const formRef = React.useRef<HTMLFormElement>(null)
  const [isPending, setPending] = React.useState(false)
  const [defaultValues, setDefaultValues] = React.useState(props.defaultValues)
  const [errors, setErrors] = React.useState<
    { [K in keyof TData]?: string[] } | undefined
  >()

  async function handleOnSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    if (!props.onAction) return
    setPending(true)

    const entriesdData = fromEntries<TData>(new FormData(e.currentTarget))
    setDefaultValues(entriesdData)

    // الحقق من القيم اذا تم توفير الـ سكيما
    const validate = props.zodSchema?.safeParse(entriesdData)

    if (validate?.success === false) {
      setErrors(validate?.error.formErrors.fieldErrors)
      setPending(false)
      return
    }

    const data = (validate?.data ?? entriesdData) as TData

    // ارسال البيانات عبر دالة السيرفر
    const { success, errors, message } = await props.onAction({ data })
    setErrors(errors)

    // الإجراء غير ناجح
    if (!success) {
      if (message) toast.error(message)
      setPending(false)
      return
    }

    if (message) toast.success(message)

    // لمعرفة الزر الذي تم النقر عليه
    const submitter = (e.nativeEvent as SubmitEvent)
      .submitter as HTMLButtonElement
    const actionType = submitter?.value

    // إجراء وبدء من جديد
    if (actionType === "actionAndStartOver" && props.mode === "create") {
      formRef.current?.reset()
      setDefaultValues(undefined)
      window.scrollTo(0, 0)
      setPending(false)
      return
    }

    if (props.callbackUrl) router.replace(props.callbackUrl)
    setPending(false)
  }

  return (
    <form
      ref={formRef}
      className="space-y-10"
      onSubmit={handleOnSubmit}
      encType="application/json"
    >
      {props.fields.map((field) => (
        <DataFormField
          {...field}
          isPending={isPending}
          key={field.accessorKey}
          errorMessage={errors?.[field.accessorKey]}
          defaultValue={defaultValues?.[field.accessorKey]}
        />
      ))}

      <ActionButtons
        mode={props.mode}
        isPending={isPending}
        callbackUrl={props.callbackUrl}
        actionButtonLabel={props.actionButtonLabel}
        cancelButtonLabel={props.cancelButtonLabel}
        actionAndStartOverButtonLabel={props.actionAndStartOverButtonLabel}
      />
    </form>
  )
}

type ActionButtonsProps = {
  mode: "create" | "update"
  actionButtonLabel?: string
  cancelButtonLabel?: string
  actionAndStartOverButtonLabel?: string
  callbackUrl?: string
  isPending: boolean
}
function ActionButtons(props: ActionButtonsProps) {
  return (
    <div className="flex w-full flex-col gap-2 md:flex-row-reverse">
      {props.actionButtonLabel && (
        <Button type="submit" isLoading={props.isPending}>
          {props.actionButtonLabel}
        </Button>
      )}
      {props.actionAndStartOverButtonLabel && props.mode === "create" && (
        <Button
          type="submit"
          variant="light"
          disabled={props.isPending}
          value="actionAndStartOver"
        >
          {props.actionAndStartOverButtonLabel}
        </Button>
      )}
      {props.cancelButtonLabel && (
        <Button
          asChild
          type="button"
          variant="light"
          disabled={props.isPending}
          value="actionAndStartOver"
        >
          <Link href={props.callbackUrl ?? "#"}>{props.cancelButtonLabel}</Link>
        </Button>
      )}
    </div>
  )
}
