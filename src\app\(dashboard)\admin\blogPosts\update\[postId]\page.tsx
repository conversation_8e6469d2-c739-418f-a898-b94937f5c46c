import { revalidatePath, revalidateTag } from "next/cache"
import { notFound } from "next/navigation"
import { dataLimits } from "@/utils/siteConfig"

import prisma from "@/lib/prisma"
import BlogPostForm from "@/components/dashboard-workspace/blogPosts-CRUD/BlogPostsForm"
import { blogPostSchema } from "@/components/dashboard-workspace/blogPosts-CRUD/blogPostsSchema"
import PageLayout from "@/components/dashboard-workspace/PageLayout"
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles"

export default async function BlogPostUpdatePage(props: {
  params: Promise<{ postId: string }>
}) {
  const { postId } = await props.params
  const blogPost = await prisma.blogPost.findUnique({ where: { id: postId } })
  if (!blogPost) return notFound()

  return (
    <PageLayout title="تعديل المنشور">
      <BlogPostForm
        mode="update"
        defaultValues={blogPost}
        onAction={async ({ data: request }) => {
          "use server"

          const { success, data, error } = blogPostSchema.safeParse(request)
          if (!success)
            return { success: false, errors: error.formErrors.fieldErrors }

          const image = await handleFileUpdate(
            blogPost.image,
            data.image,
            "blog/image/"
          )

          const [updatedBlogPost, blogPostsBefore] = await prisma.$transaction([
            prisma.blogPost.update({
              where: { id: data.id },
              data: { ...data, image },
            }),

            prisma.blogPost.count({
              where: {
                createdAt: {
                  gt: blogPost.createdAt,
                },
              },
            }),
          ])

          const pageIndex =
            Math.floor(blogPostsBefore / dataLimits.blogPost) + 1

          revalidateTag("pageTitlesAndIds")
          revalidatePath("/admin/blogPosts")
          revalidatePath(`/blog/${pageIndex}`)
          revalidatePath(`/blog/${pageIndex}/${updatedBlogPost.id}`)

          return { success: true, message: "تم تعديل المنشور بنجاح" }
        }}
      />
    </PageLayout>
  )
}
