
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.6.0
 * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
 */
Prisma.prismaVersion = {
  client: "6.6.0",
  engine: "f676762280b54cd07c770017ed3711ddde35f37a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.CourseScalarFieldEnum = {
  id: 'id',
  title: 'title',
  price: 'price',
  posterUrl: 'posterUrl',
  features: 'features',
  description: 'description',
  modulesCount: 'modulesCount',
  previewInHomePage: 'previewInHomePage',
  createdAt: 'createdAt',
  seoDescription: 'seoDescription',
  seokeywords: 'seokeywords'
};

exports.Prisma.LectureScalarFieldEnum = {
  id: 'id',
  title: 'title',
  posterUrl: 'posterUrl',
  courseId: 'courseId',
  video: 'video',
  audio: 'audio',
  pdf: 'pdf',
  seoDescription: 'seoDescription',
  seokeywords: 'seokeywords',
  createdAt: 'createdAt'
};

exports.Prisma.TestimonyScalarFieldEnum = {
  id: 'id',
  testimony: 'testimony'
};

exports.Prisma.ArticleScalarFieldEnum = {
  id: 'id',
  title: 'title',
  article: 'article',
  createdAt: 'createdAt',
  seoDescription: 'seoDescription',
  seokeywords: 'seokeywords'
};

exports.Prisma.BookScalarFieldEnum = {
  id: 'id',
  title: 'title',
  summary: 'summary',
  coverImage: 'coverImage',
  pagesCount: 'pagesCount',
  createdAt: 'createdAt',
  price: 'price',
  bookUrl: 'bookUrl',
  seoDescription: 'seoDescription',
  seokeywords: 'seokeywords'
};

exports.Prisma.InterviewScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  videoUrl: 'videoUrl',
  createdAt: 'createdAt',
  thumbnail: 'thumbnail',
  seoDescription: 'seoDescription',
  seokeywords: 'seokeywords'
};

exports.Prisma.BlogPostScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  createdAt: 'createdAt',
  image: 'image',
  seoDescription: 'seoDescription',
  seokeywords: 'seokeywords'
};

exports.Prisma.CommentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  userName: 'userName',
  entityId: 'entityId',
  comment: 'comment',
  entity: 'entity',
  createdAt: 'createdAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  emailVerified: 'emailVerified',
  verificationTokenExpiry: 'verificationTokenExpiry',
  verificationToken: 'verificationToken'
};

exports.Prisma.CourseOrderScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  courseId: 'courseId',
  price: 'price',
  status: 'status',
  createdAt: 'createdAt'
};

exports.Prisma.BookOrderScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  bookId: 'bookId',
  price: 'price',
  status: 'status',
  createdAt: 'createdAt'
};

exports.Prisma.ConsultationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  read: 'read',
  email: 'email',
  phone: 'phone',
  message: 'message',
  createdAt: 'createdAt'
};

exports.Prisma.FaqScalarFieldEnum = {
  id: 'id',
  question: 'question',
  answer: 'answer',
  createdAt: 'createdAt'
};

exports.Prisma.AdminScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  accessiblePages: 'accessiblePages',
  avatarUrl: 'avatarUrl',
  pushSubscription: 'pushSubscription',
  status: 'status',
  role: 'role'
};

exports.Prisma.SiteSettingsScalarFieldEnum = {
  id: 'id',
  phone: 'phone',
  email: 'email',
  facebookUrl: 'facebookUrl',
  twitterUrl: 'twitterUrl',
  instagramUrl: 'instagramUrl',
  youtubeUrl: 'youtubeUrl',
  tiktokUrl: 'tiktokUrl',
  myStoryVideoUrl: 'myStoryVideoUrl',
  myStoryVideoThumbnailUrl: 'myStoryVideoThumbnailUrl',
  subscriptionInstructions: 'subscriptionInstructions'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.EntityType = exports.$Enums.EntityType = {
  lecture: 'lecture',
  book: 'book',
  interview: 'interview',
  article: 'article',
  blogPost: 'blogPost'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  FAILED: 'FAILED'
};

exports.AdminStatus = exports.$Enums.AdminStatus = {
  activated: 'activated',
  disabled: 'disabled'
};

exports.AdminRole = exports.$Enums.AdminRole = {
  superadmin: 'superadmin',
  admin: 'admin'
};

exports.Prisma.ModelName = {
  Course: 'Course',
  Lecture: 'Lecture',
  Testimony: 'Testimony',
  Article: 'Article',
  Book: 'Book',
  Interview: 'Interview',
  BlogPost: 'BlogPost',
  Comment: 'Comment',
  User: 'User',
  CourseOrder: 'CourseOrder',
  BookOrder: 'BookOrder',
  Consultation: 'Consultation',
  Faq: 'Faq',
  Admin: 'Admin',
  SiteSettings: 'SiteSettings'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
