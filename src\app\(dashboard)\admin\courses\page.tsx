import { revalidatePath, revalidateTag } from "next/cache"
import { getCourses } from "@/utils/get-data-from-db"
import { z } from "zod"

import { revalidateCourses } from "@/lib/cache-invalidation"
import prisma from "@/lib/prisma"
import CoursesTable from "@/components/dashboard-workspace/courses-CRUD/CoursesTable"
import { deleteFileInR2 } from "@/components/dashboard/components/fileUploader/handleFiles"

export default async function CoursesPage() {
  const courses = await getCourses()

  return (
    <CoursesTable
      data={courses}
      onOptionChange={async ({ where, data: request }) => {
        "use server"

        const schema = z.object({
          previewInHomePage: z.preprocess((val) => {
            if (val === "true") return true
            if (val === "false") return false
            return val // إذا كانت القيمة غير متوقعة، تمرر كما هي وتفشل لاحقًا عند التحقق
          }, z.boolean()),
        })

        const { success, data } = schema.safeParse(request)
        if (!success)
          return {
            success: false,
            message: "حدث خطأ اثناء محاولة تغيير القيمة",
          }

        await prisma.course.update({
          where: { id: where.id },
          data: { previewInHomePage: data.previewInHomePage },
        })

        revalidatePath(`/`)
        revalidateTag("pageTitlesAndIds")
        revalidatePath(`/admin/courses`)
        await revalidateCourses()

        return { success: true, message: "تم تغيير القيمة بنجاح" }
      }}
      onDelete={async ({ data }) => {
        "use server"

        const deleteCourses = await prisma.$transaction(
          data.map((course) =>
            prisma.course.delete({
              where: { id: course.id },
              include: { lectures: true },
            })
          )
        )

        const deleteFile = (sourse: string | null) =>
          sourse ? deleteFileInR2({ fileUrl: sourse }) : Promise.resolve()

        await Promise.all(
          deleteCourses.flatMap((course) => [
            deleteFile(course.posterUrl),
            ...course.lectures.flatMap((lecture) => [
              deleteFile(lecture.posterUrl),
              deleteFile(lecture.video),
              deleteFile(lecture.audio),
              deleteFile(lecture.pdf),
            ]),
          ])
        )

        revalidateTag("pageTitlesAndIds")
        revalidatePath(`/courses`)
        revalidatePath(`/admin/courses`)

        deleteCourses.map((course) => {
          course.lectures.map((lecture) =>
            revalidatePath(`/courses/${course.id}/${lecture.id}`)
          )

          revalidatePath(`/admin/courses/lectures/${course.id}`)
          revalidatePath(`/courses/${course.id}`)
        })

        if (deleteCourses.find((course) => course.previewInHomePage))
          revalidatePath(`/`)

        await revalidateCourses()

        return {
          success: true,
          message: `تم حذف ${deleteCourses.length} من الكورسات وجميع المحاور التابعة لها`,
        }
      }}
    />
  )
}
