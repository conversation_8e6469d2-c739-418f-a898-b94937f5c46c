import * as React from "react"
import { Metadata } from "next"
import Link from "next/link"
import { notFound } from "next/navigation"
import { Article } from "@prisma/client"
import { CalendarDays } from "lucide-react"

import prisma from "@/lib/prisma"
import Comments from "@/components/ui/comments/comments"
import FormatTime from "@/components/ui/format-time"
import PageDivider from "@/components/ui/page-divider"

type ArticlePageProps = { params: Promise<{ articleId?: string }> }

// جلب بيانات صفحة المقالة
// ترجع كائن محتوى المقالة ومصفوفة من الكائنات لعشر مقالات تالية
const getArticleAndRelated = React.cache(async (articleId?: string) => {
  const articles = await prisma.article.findMany({
    cursor: { id: articleId },
    take: 11,
  })

  const article = articles.find((article) => article.id === articleId)
  const related = articles?.filter((article) => article.id !== articleId)

  if (!article) return notFound()
  return { article, related }
})

export async function generateMetadata(
  props: ArticlePageProps
): Promise<Metadata> {
  const articleId = (await props.params).articleId
  const { article } = await getArticleAndRelated(articleId)

  return {
    title: article.title,
    description: article.seoDescription,
    keywords: article.seokeywords,
  }
}

// ==================================================================================
// صفحة المقاله
// ==================================================================================
export default async function ArticlePage(props: ArticlePageProps) {
  const articleId = (await props.params)?.articleId
  const { article, related } = await getArticleAndRelated(articleId)

  return (
    <PageDivider
      pageContents={
        <div className="flex-1 space-y-5">
          <h1 className="text-primary text-2xl font-bold md:text-3xl">
            {article.title}
          </h1>
          <div
            className="textHTML"
            dangerouslySetInnerHTML={{ __html: article.article }}
          ></div>
        </div>
      }
      titleMenu="مقالات ذات صلة"
      menuItems={
        <div className="snap-y space-y-4">
          {related?.map((article) => (
            <ArticleMenuCard key={article.id} article={article} />
          ))}
        </div>
      }
      comments={
        <Comments
          entity="article"
          entityId={article.id}
          pathRevalidate={`/articles/${article.id}`}
        />
      }
    />
  )
}

// ==================================================================================
// مكون بطاقة المقالة في قائمة المحتوى ذات صلة
// يتم استخدامها في هذه الصفحة وفي صفحة المقالة
// ==================================================================================
function ArticleMenuCard({ article }: { article: Article }) {
  return (
    <div className="text-secondary w-full space-y-5 overflow-clip rounded-md bg-white shadow-[0_0_15px_-2px_#00000040]">
      <div className="w-full space-y-2 px-4 pt-3 md:space-y-3 md:pt-5">
        <Link
          href={`/articles/${article.id}`}
          className="text-primary hover:text-primary/90 flex w-full text-base font-bold underline underline-offset-5"
        >
          <span className="truncate">{article.title}</span>
        </Link>
        <p className="mt-1.5 line-clamp-3 text-sm leading-relaxed">
          {article.seoDescription}
        </p>
      </div>
      <div className="bg-muted/10 w-full rounded-sm px-4 py-2">
        <span className="flex flex-nowrap items-center gap-2 text-xs text-nowrap">
          <CalendarDays className="size-4" />{" "}
          <FormatTime dateInput={article.createdAt} />
        </span>
      </div>
    </div>
  )
}
